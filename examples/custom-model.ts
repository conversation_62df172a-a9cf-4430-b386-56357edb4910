/**
 * Example of implementing a custom text-to-SQL model
 */

import { TextToSQLModel } from '../src/models/text-to-sql';
import { WikiSQLQuery, WikiSQLTable } from '../src/types/wikisql';

/**
 * Example custom text-to-SQL model that uses simple keyword matching
 */
export class KeywordBasedTextToSQLModel implements TextToSQLModel {
  async predict(question: string, table: WikiSQLTable): Promise<WikiSQLQuery> {
    const lowerQuestion = question.toLowerCase();
    
    // Default query structure
    let sel = 0;
    let agg = 0;
    let conds: [number, number, string | number][] = [];

    // Simple aggregation detection
    if (lowerQuestion.includes('how many') || lowerQuestion.includes('count')) {
      agg = 3; // COUNT
    } else if (lowerQuestion.includes('maximum') || lowerQuestion.includes('max')) {
      agg = 1; // MAX
    } else if (lowerQuestion.includes('minimum') || lowerQuestion.includes('min')) {
      agg = 2; // MIN
    } else if (lowerQuestion.includes('sum') || lowerQuestion.includes('total')) {
      agg = 4; // SUM
    } else if (lowerQuestion.includes('average') || lowerQuestion.includes('avg')) {
      agg = 5; // AVG
    }

    // Simple column selection based on question words
    const columnKeywords = [
      ['name', 'player', 'who'],
      ['number', 'num', '#'],
      ['position', 'pos'],
      ['school', 'college', 'university'],
      ['year', 'season'],
      ['team']
    ];

    for (let i = 0; i < table.header.length; i++) {
      const header = table.header[i].toLowerCase();
      for (const keywords of columnKeywords) {
        if (keywords.some(keyword => 
          lowerQuestion.includes(keyword) && header.includes(keyword)
        )) {
          sel = i;
          break;
        }
      }
    }

    // Simple condition detection
    // Look for quoted strings or numbers that might be conditions
    const quotedMatches = question.match(/"([^"]+)"/g);
    const numberMatches = question.match(/\b\d+\b/g);
    
    if (quotedMatches) {
      for (const match of quotedMatches) {
        const value = match.replace(/"/g, '');
        // Try to find a column that might match this value
        for (let i = 0; i < table.header.length; i++) {
          const header = table.header[i].toLowerCase();
          if (header.includes('name') || header.includes('school') || header.includes('team')) {
            conds.push([i, 0, value]); // equals condition
            break;
          }
        }
      }
    }

    if (numberMatches && conds.length === 0) {
      for (const match of numberMatches) {
        const value = parseInt(match, 10);
        // Try to find a numeric column
        for (let i = 0; i < table.header.length; i++) {
          const header = table.header[i].toLowerCase();
          if (header.includes('number') || header.includes('year') || header.includes('#')) {
            conds.push([i, 0, value]); // equals condition
            break;
          }
        }
      }
    }

    return { sel, agg, conds };
  }

  getName(): string {
    return "KeywordBasedTextToSQLModel";
  }
}

// Example usage:
async function example() {
  const model = new KeywordBasedTextToSQLModel();
  
  // Example table
  const table: WikiSQLTable = {
    id: "example-1",
    header: ["Player", "Number", "Position", "School", "Year"],
    types: ["text", "text", "text", "text", "text"],
    rows: [
      ["John Doe", "23", "Guard", "UCLA", "2020"],
      ["Jane Smith", "15", "Forward", "Duke", "2019"]
    ]
  };

  // Example questions
  const questions = [
    "What school did player number 23 attend?",
    "How many players are from UCLA?",
    "Who is the player that wears number 15?"
  ];

  for (const question of questions) {
    const prediction = await model.predict(question, table);
    console.log(`Question: ${question}`);
    console.log(`Prediction:`, prediction);
    console.log('---');
  }
}

// Uncomment to run the example
// example().catch(console.error);
